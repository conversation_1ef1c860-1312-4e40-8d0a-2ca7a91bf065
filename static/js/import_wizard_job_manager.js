/**
 * Import Wizard Job Management
 */

// Add save debouncing to prevent multiple concurrent save requests
let saveInProgress = false;
let saveTimeout = null;

/**
 * PERFORMANCE: Get optimized column results to reduce memory usage
 */
function getOptimizedColumnResults(columnResults) {
    if (!columnResults || typeof columnResults !== 'object') {
        return {};
    }
    
    const optimized = {};
    
    for (const [columnName, results] of Object.entries(columnResults)) {
        if (Array.isArray(results)) {
            // For large result sets, only save key information
            if (results.length > 100) {
                // Check if all values are the same (string values)
                const firstValue = results[0];
                const allSame = results.every(val => val === firstValue);
                
                if (allSame) {
                    // Store as a single value with length info
                    optimized[columnName] = {
                        __optimized: true,
                        type: 'uniform',
                        value: firstValue,
                        length: results.length
                    };
                } else {
                    // Store a compressed version with samples
                    optimized[columnName] = {
                        __optimized: true,
                        type: 'varied',
                        sample: results.slice(0, 10),
                        length: results.length,
                        lastModified: Date.now()
                    };
                }
            } else {
                // For small result sets, store normally
                optimized[columnName] = results;
            }
        } else {
            // Non-array values, store as is
            optimized[columnName] = results;
        }
    }
    
    return optimized;
}

/**
 * PERFORMANCE: Reconstruct column results from optimized format
 */
function reconstructColumnResults(optimizedResults) {
    if (!optimizedResults || typeof optimizedResults !== 'object') {
        return {};
    }
    
    const reconstructed = {};
    
    for (const [columnName, data] of Object.entries(optimizedResults)) {
        if (data && typeof data === 'object' && data.__optimized) {
            if (data.type === 'uniform') {
                // Reconstruct uniform array
                reconstructed[columnName] = new Array(data.length).fill(data.value);
            } else if (data.type === 'varied') {
                // For varied data, we need to reconstruct or fetch from server
                // For now, use the sample data
                reconstructed[columnName] = data.sample;
                console.log(`⚠️ Column ${columnName} has ${data.length} results but only ${data.sample.length} loaded`);
            }
        } else {
            // Normal data, use as is
            reconstructed[columnName] = data;
        }
    }
    
    return reconstructed;
}

// --- Job Management Functions ---
function loadImportJob(jobId) {
    if (!jobId) return;
    console.log(`Loading import job: ${jobId}`);
    
    // PERFORMANCE: Clean up memory before loading new job
    if (window.MemoryManager && typeof window.MemoryManager.cleanupMemory === 'function') {
        window.MemoryManager.cleanupMemory();
    }
    
    $('#currentJobDisplay').hide(); // Hide initially, show on success
    const mappingSection = $('#mappingSection');
    const horizontalContainer = $('#horizontal-mapping-container');
    if (mappingSection.length && horizontalContainer.length) {
        horizontalContainer.html('<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading job...</span></div><p>Loading job data...</p></div>');
    }

    fetch(`/api/import/jobs/${jobId}`)
        .then(response => {
            if (!response.ok) {
                if (response.status === 404) throw new Error(`Job ${jobId} not found.`);
                throw new Error('Network response was not ok while fetching job data.');
            }
            return response.json();
        })
        .then(jobData => {
            currentJobId = jobData.job_id;
            originalData = jobData.original_data || [];
            mappingDefinitions = jobData.mapping_definitions || [];
            columnMappings = jobData.column_mappings || {};
            columnLogic = jobData.column_logic || {};
            columnStringValues = jobData.column_string_values || {};
            columnLlmSettings = jobData.column_llm_settings || {};
            userNotes = jobData.user_notes || '';
            mappedData = jobData.mapped_data || [];
            const deactivatedFields = jobData.deactivated_fields || [];

            // Debug logging for data loading
            console.log(`📊 Job data loaded: ${originalData.length} rows, ${mappingDefinitions.length} mapping definitions`);
            console.log(`🔧 Column configurations: mappings=${Object.keys(columnMappings).length}, logic=${Object.keys(columnLogic).length}, strings=${Object.keys(columnStringValues).length}`);
            
            // Load UI column order if available
            window.uiColumnOrder = jobData.ui_column_order || [];

            // Load column results if available (with deduplication support)
            if (jobData.column_results) {
                console.log("📥 Loading saved column results from job data");
                columnResults = reconstructColumnResults(jobData.column_results);
                console.log(`📊 Reconstructed results for ${Object.keys(columnResults).length} columns`);
            } else {
                console.log("No saved column results found, initializing empty object");
                columnResults = {};
            }

            updateJobNameInHeader(jobData.job_name || `Import Job - ${jobData.job_id.substring(0,8)}`);

            $('#userNotes').val(userNotes);
            if (jobData.original_filename) {
                console.log("Original filename: " + jobData.original_filename);
            }

            // Extract source columns before initializing column results
            extractSourceColumns();

            // Now initialize column results if they're missing or incomplete
            console.log("Initializing missing column results");
            initializeColumnResults();
            
            mappingSection.removeClass('d-none');
            
            $('#saveProgressButton').show();
            
            // Note: Row/column count info is now handled by updateHeaderRowColumnInfo() called from updateJobNameInHeader()
            
            console.log("Mapping definitions to be used by generateMappingUI:", mappingDefinitions);
            
            // Generate the mapping UI - this will create all the column elements
            generateMappingUI();
            
            // Apply deactivated fields
            if (deactivatedFields && deactivatedFields.length > 0) {
                setTimeout(() => {
                    deactivatedFields.forEach(field => {
                        const columnElement = $(`.mapping-column-cell[data-target-column="${field}"]`);
                        if (columnElement.length) {
                            const mapTypeSelect = columnElement.find('.map-type-select');
                            mapTypeSelect.val('deactivated');
                            mapTypeSelect.trigger('change');
                        }
                    });
                }, 100);
            }
            
            // Make sure the UI shows our saved column results
            console.log("Updating preview grid with saved column results");
            // Use safe wrapper with retry logic
            if (typeof safeUpdateUnifiedPreviewGrid === 'function') {
                safeUpdateUnifiedPreviewGrid();
            } else if (typeof updateUnifiedPreviewGrid === 'function') {
                updateUnifiedPreviewGrid();
            } else {
                console.warn("Preview grid functions not available yet, will retry...");
                // Retry after a short delay to allow scripts to load
                setTimeout(() => {
                    if (typeof safeUpdateUnifiedPreviewGrid === 'function') {
                        safeUpdateUnifiedPreviewGrid();
                    } else if (typeof updateUnifiedPreviewGrid === 'function') {
                        updateUnifiedPreviewGrid();
                    } else {
                        console.error("Preview grid functions still not available after retry");
                    }
                }, 1000);
            }
            
            if (mappedData.length > 0) {
                displayMappedData();
            }
            
            // Restore workflow button states
            if (originalData.length > 0 && mappingDefinitions.length > 0) {
                $('#workflowSteps').removeClass('d-none').addClass('d-flex');

                // Check if Product Family has been determined
                // This is a placeholder condition. We need a more robust way to check this.
                // For example, check if 'Product Family' exists as a key in columnResults and has values.
                const productFamilyDetermined = columnResults && columnResults['Product Family'] && columnResults['Product Family'].some(entry => entry.value !== null && entry.value !== undefined && entry.value !== '');

                if (productFamilyDetermined) {
                    $('#determineProductFamilyBtn').prop('disabled', true);
                    // Further logic for subsequent buttons can be added here
                } else {
                    $('#determineProductFamilyBtn').prop('disabled', false);
                }
                // Ensure other buttons are in their initial state or determined by subsequent logic
                $('#mapDataBtn').prop('disabled', true); // Assuming this is the next step and should be initially disabled
            } else {
                // If no data or definitions, ensure workflow is hidden or reset
                 $('#workflowSteps').addClass('d-none').removeClass('d-flex');
                 $('#determineProductFamilyBtn').prop('disabled', true);
                 $('#mapDataBtn').prop('disabled', true);
            }
            
            $('#importSection').removeClass('show');
            showAlert('info', `Loaded import job: ${jobId}`);

            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('job_id') !== currentJobId) {
                const newUrl = window.location.pathname + '?job_id=' + currentJobId + window.location.hash;
                history.pushState({path:newUrl}, '', newUrl);
            }

            // Set up auto-save for this job
            setupAutoSave();

            // Set job ID in product family handler
            if (window.productFamilyHandler) {
                window.productFamilyHandler.setCurrentJobId(currentJobId);
                window.productFamilyHandler.showWorkflowSteps();
            }
        })
        .catch(error => {
            console.error('Error loading job:', error);
            showAlert('error', 'Error loading job: ' + error.message);
            if (horizontalContainer.length) {
                horizontalContainer.html(`<p class="text-danger p-3">Error loading job data: ${error.message}. Please try again or select a different job.</p>`);
            }
            currentJobId = null;
            $('#saveProgressButton').hide();
            history.pushState({}, '', window.location.pathname + window.location.hash);
        });
}

function saveCurrentJobProgress(silent = false) {
    if (!currentJobId) {
        if (!silent) showAlert('error', 'No active job to save.');
        return Promise.resolve();
    }

    // Prevent multiple concurrent save requests
    if (saveInProgress) {
        if (!silent) console.log('Save already in progress, skipping...');
        return Promise.resolve();
    }

    saveInProgress = true;

    let currentJobName = $('#currentJobNameDisplay').text();
    // Remove the row/column count suffix if present
    const countText = currentJobName.match(/\(\d+ rows, \d+ columns\)/);
    if (countText) {
        currentJobName = currentJobName.replace(countText[0], '').trim();
    }

    // Collect deactivated fields from UI
    const deactivatedFields = [];
    $('.map-type-select').each(function() {
        const targetColumn = $(this).closest('.mapping-column-cell').attr('data-target-column');
        if ($(this).val() === 'deactivated') {
            deactivatedFields.push(targetColumn);
        }
    });

    // First get the current job data to preserve transformed_data
    console.log(`🔄 Fetching current job data for ${currentJobId}`);
    
    return new Promise((resolve, reject) => {
        // Use XMLHttpRequest to avoid any fetch polyfill issues
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `/api/import/jobs/${currentJobId}`, true);
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        console.log(`✅ GET response received, length: ${xhr.responseText.length}`);
                        const data = JSON.parse(xhr.responseText);
                        resolve(data);
                    } catch (e) {
                        console.error('❌ JSON parse error:', e, 'Response text:', xhr.responseText);
                        reject(new Error(`JSON parse failed: ${e.message}`));
                    }
                } else {
                    console.error(`❌ HTTP error: ${xhr.status} ${xhr.statusText}`);
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            }
        };
        
        xhr.onerror = function() {
            console.error('❌ Network error during GET request');
            reject(new Error('Network error'));
        };
        
        xhr.send();
    })
        .then(currentJobData => {
            // PERFORMANCE OPTIMIZED: Minimize data duplication and payload size
            const jobDataToSave = {
                job_name: currentJobName,
                // For large datasets, only send metadata to reduce memory and network usage
                original_data: originalData && originalData.length > 100 ? 
                    { __metadata: { length: originalData.length, sample: originalData.slice(0, 5) } } : 
                    originalData || [],
                source_columns: sourceColumns || [],
                mapping_definitions: mappingDefinitions || [],
                
                // DEDUPLICATION: Send references instead of copies
                column_mappings: columnMappings || {},
                column_logic: columnLogic || {},
                column_string_values: columnStringValues || {},
                column_llm_settings: columnLlmSettings || {},
                
                user_notes: $('#userNotes').val() || '',
                
                // For large mapped data, only save key indicators
                mapped_data: mappedData && mappedData.length > 100 ? 
                    { __metadata: { length: mappedData.length, sample: mappedData.slice(0, 3) } } : 
                    mappedData || [],
                    
                deactivated_fields: deactivatedFields,
                
                // PERFORMANCE: Send only changed column results, not all data
                column_results: getOptimizedColumnResults(columnResults),
                
                // IMPORTANT: Preserve the backend transformed_data from LLM processing
                transformed_data: currentJobData.transformed_data || [],
                
                // Save UI column order
                ui_column_order: window.uiColumnOrder || [],
                
                status: mappedData.length > 0 && 
                        Object.keys(columnMappings).length === 0 && 
                        Object.keys(columnLogic).length === 0 && 
                        Object.keys(columnStringValues).length === 0 ? 'data-uploaded' : 'in-progress',
                last_saved_at: new Date().toISOString(),
                // Add metadata for large datasets
                data_summary: {
                    original_rows: originalData ? originalData.length : 0,
                    mapped_rows: mappedData ? mappedData.length : 0,
                    is_large_dataset: originalData && originalData.length > 100
                }
            };

            if (!silent) {
                console.log("Saving job progress for:", currentJobId);
                console.log("Data summary:", jobDataToSave.data_summary);
            }
            
            // Update UI only if not silent mode
            const saveButton = $('#saveProgressButton');
            const originalButtonHtml = saveButton.html();
            
            if (!silent) {
                saveButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> Saving...');
            }

            // Set storage flag to help with debugging
            localStorage.setItem(`job_${currentJobId}_last_save_attempt`, new Date().toISOString());

            console.log(`🔄 Saving job data for ${currentJobId}, size: ${JSON.stringify(jobDataToSave).length} chars`);
            console.log(`📋 Mappings being saved:`, {
                column_mappings: Object.keys(jobDataToSave.column_mappings || {}).length,
                column_logic: Object.keys(jobDataToSave.column_logic || {}).length, 
                column_string_values: Object.keys(jobDataToSave.column_string_values || {}).length
            });
            
            return new Promise((resolve, reject) => {
                // Use XMLHttpRequest to avoid any fetch polyfill issues  
                const xhr = new XMLHttpRequest();
                xhr.open('PUT', `/api/import/jobs/${currentJobId}`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                console.log(`✅ PUT response received, length: ${xhr.responseText.length}`);
                                if (xhr.responseText && xhr.responseText.trim()) {
                                    const data = JSON.parse(xhr.responseText);
                                    resolve(data);
                                } else {
                                    // Empty response is OK for PUT
                                    resolve({ success: true });
                                }
                            } catch (e) {
                                console.error('❌ PUT JSON parse error:', e, 'Response text:', xhr.responseText);
                                reject(new Error(`JSON parse failed: ${e.message}`));
                            }
                        } else {
                            console.error(`❌ PUT HTTP error: ${xhr.status} ${xhr.statusText}`);
                            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                        }
                    }
                };
                
                xhr.onerror = function() {
                    console.error('❌ Network error during PUT request');
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(jobDataToSave));
            })
            .then(data => {
                if (data.success) {
                    // Set a flag in local storage to mark this job as successfully saved
                    localStorage.setItem(`job_${currentJobId}_last_save_success`, new Date().toISOString());
                    
                    if (!silent) showAlert('success', 'Progress saved successfully!');
                } else {
                    if (!silent) showAlert('error', data.message || 'Failed to save progress.');
                }
            })
            .catch(error => {
                console.error('Error saving job progress:', error);
                if (!silent) showAlert('error', 'Error saving progress: ' + error.message);
            })
            .finally(() => {
                if (!silent) {
                    saveButton.prop('disabled', false).html(originalButtonHtml);
                }
                saveInProgress = false; // Reset the flag when save completes
            });
        })
        .catch(error => {
            console.error('Error loading current job data for save:', error);
            if (!silent) showAlert('error', 'Error loading current job data: ' + error.message);
            saveInProgress = false; // Reset the flag on error
        });
}

function updateJobNameInHeader(jobName) {
    const displayName = jobName || (currentJobId ? `Import Job - ${currentJobId.substring(0,8)}` : 'New Import');
    $('#currentJobNameDisplay').text(displayName);
    
    // Add row/column information if we have data
    updateHeaderRowColumnInfo();
    
    if (currentJobId) {
        $('#currentJobDisplay').show();
    } else {
        $('#currentJobDisplay').hide();
    }
}

/**
 * Update the row and column count information in the header
 */
function updateHeaderRowColumnInfo() {
    const jobNameElement = $('#currentJobNameDisplay');
    
    // Remove any existing count information
    jobNameElement.find('.row-col-info').remove();
    
    // Add new count information if we have data
    if (originalData && originalData.length > 0) {
        // Count the current number of mapping columns in the UI, excluding deactivated ones
        let activeColumnCount = 0;
        $('#horizontal-mapping-container .mapping-column-cell').each(function() {
            const targetColumn = $(this).attr('data-target-column');
            const mapTypeSelect = $(this).find('.map-type-select');
            const isDeactivated = (window.deactivatedColumns && window.deactivatedColumns[targetColumn]) ||
                                 (mapTypeSelect.length && mapTypeSelect.val() === 'deactivated');
            if (!isDeactivated) {
                activeColumnCount++;
            }
        });
        const totalColumnCount = activeColumnCount > 0 ? activeColumnCount : sourceColumns.length;
        
        const rowColInfo = $('<span>')
            .addClass('ms-2 small text-muted row-col-info')
            .text(`(${originalData.length} rows, ${totalColumnCount} columns)`);
        jobNameElement.append(rowColInfo);
    }
}

function renameCurrentJob(newName) {
    if (!currentJobId) {
        showAlert('error', 'Cannot rename: No active job ID.');
        return;
    }
    fetch(`/api/import/jobs/${currentJobId}/rename`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ job_name: newName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || `Job renamed successfully to "${newName}".`);
            updateJobNameInHeader(newName);
            // If we are maintaining a local jobData object, update it too.
            // For now, loadImportJob or the next saveProgress will handle persisting it more broadly.
        } else {
            showAlert('error', data.message || 'Failed to rename job.');
        }
    })
    .catch(error => {
        console.error('Error renaming job:', error);
        showAlert('danger', `Error renaming job: ${error.message}`);
    });
}

/**
 * Setup auto-save functionality with performance optimization
 */
function setupAutoSave() {
    // Clear any existing interval
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
    }
    
    // Determine auto-save interval based on dataset size
    let saveDelay = AUTO_SAVE_DELAY;
    if (originalData && originalData.length > 500) {
        saveDelay = AUTO_SAVE_DELAY_LARGE_DATASET;
        console.log(`Large dataset detected (${originalData.length} rows), using extended auto-save interval: ${saveDelay/1000}s`);
    }
    
    // Set up new auto-save interval
    autoSaveInterval = setInterval(function() {
        if (currentJobId) {
            saveCurrentJobProgress(true); // true = silent mode (no UI alerts)
        }
    }, saveDelay);
    
    // Also save when user leaves the page
    $(window).on('beforeunload', function() {
        if (currentJobId) {
            // Use synchronous request for beforeunload
            saveCurrentJobProgressSync();
        }
    });
    
    // Setup memory cleanup for large datasets
    if (originalData && originalData.length > 100) {
        setupMemoryCleanup();
    }
}

/**
 * Synchronous version of save for beforeunload events
 */
function saveCurrentJobProgressSync() {
    if (!currentJobId) return;
    
    try {
        // Get job data similar to the async version
        let currentJobName = $('#currentJobNameDisplay').text();
        const countText = currentJobName.match(/\(\d+ rows, \d+ columns\)/);
        if (countText) {
            currentJobName = currentJobName.replace(countText[0], '').trim();
        }
        
        const deactivatedFields = [];
        $('.mapping-column-cell').each(function() {
            const column = $(this);
            const targetColumn = column.attr('data-target-column');
            const mapTypeSelect = column.find('.map-type-select');
            
            if (mapTypeSelect.length && mapTypeSelect.val() === 'deactivated') {
                deactivatedFields.push(targetColumn);
            }
        });

        // Get current job data synchronously to preserve transformed_data
        let currentJobData = {};
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `/api/import/jobs/${currentJobId}`, false); // false = synchronous
            xhr.send();
            if (xhr.status === 200) {
                currentJobData = JSON.parse(xhr.responseText);
            }
        } catch (e) {
            console.error('Error loading current job data for sync save:', e);
        }

        const jobDataToSave = {
            job_name: currentJobName,
            original_data: originalData,
            source_columns: sourceColumns,
            mapping_definitions: mappingDefinitions,
            column_mappings: columnMappings,
            column_logic: columnLogic,
            column_string_values: columnStringValues,
            column_llm_settings: columnLlmSettings,
            user_notes: $('#userNotes').val(),
            mapped_data: mappedData,
            deactivated_fields: deactivatedFields,
            column_results: columnResults,
            // IMPORTANT: Preserve the backend transformed_data from LLM processing
            transformed_data: currentJobData.transformed_data || [],
            // Save UI column order
            ui_column_order: window.uiColumnOrder || [],
            status: 'in-progress',
            last_saved_at: new Date().toISOString(),
            is_exit_save: true // Flag to indicate this was saved on exit
        };

        // Use synchronous AJAX for beforeunload
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', `/api/import/jobs/${currentJobId}`, false); // false = synchronous
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.send(JSON.stringify(jobDataToSave));
        
        // Set a storage flag
        localStorage.setItem(`job_${currentJobId}_exit_save_attempt`, new Date().toISOString());
    } catch (e) {
        console.error('Error in sync save:', e);
    }
}

/**
 * Debounced version of saveCurrentJobProgress for auto-save during AI mapping
 */
function debouncedSaveCurrentJobProgress(silent = true) {
    // Clear any existing timeout
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }
    
    // Set a new timeout
    saveTimeout = setTimeout(() => {
        saveCurrentJobProgress(silent);
    }, 1000); // Wait 1 second after last call
}