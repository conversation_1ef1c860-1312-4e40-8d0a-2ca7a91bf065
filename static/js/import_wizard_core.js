/**
 * Import Wizard Core Logic
 * Handles global variables and main initialization.
 */

// Global variables
let originalData = []; // Raw data from the file
let sourceColumns = []; // Column definitions from source data
let mappingDefinitions = []; // Column mappings from Excel file
let mappedData = []; // Data after mapping transformation

// AG Grid API instances - attach to window for wider accessibility
window.gridApi = null; // Legacy grid API reference (may be used by some components)
window.mappedGridApi = null; // Mapped data grid API reference

let columnMappings = {}; // Current active mappings
let columnLogic = {}; // Custom logic for columns
let columnStringValues = {}; // Static string values for columns
let columnLlmSettings = {}; // LLM settings for logic columns { targetColumn: { model: 'x', temperature: 0.y } }
let userNotes = ''; // User notes that persist during the session
let currentJobId = null; // ID of the currently active import job
let unifiedPreviewData = []; // Data for the unified preview grid
let previewRowLimit = 1000; // Initial row limit for preview - reasonable default
let aiMappingModal = null;
let logicApplyRowsDefault = 3; // Default number of rows to apply logic to
let autoSaveInterval = null; // Will hold the interval ID for auto-saving

/**
 * Safe wrapper for calling updateUnifiedPreviewGrid with proper error handling
 */
function safeUpdateUnifiedPreviewGrid(targetColumn = null, newRowData = null) {
    try {
        // Check if the grid system is ready
        if (!window.unifiedPreviewGridReady) {
            console.warn('Unified preview grid system not ready yet');
            return;
        }

        if (typeof updateUnifiedPreviewGrid === 'function') {
            updateUnifiedPreviewGrid(targetColumn, newRowData);
        } else {
            console.warn('updateUnifiedPreviewGrid function not available yet');
        }
    } catch (error) {
        console.error('Error calling updateUnifiedPreviewGrid:', error);
    }
}

// PERFORMANCE OPTIMIZATIONS: Reduced auto-save frequency for large datasets
const AUTO_SAVE_DELAY = 180000; // Auto-save every 3 minutes (180000ms) instead of 1 minute
const AUTO_SAVE_DELAY_LARGE_DATASET = 300000; // Auto-save every 5 minutes for large datasets (300000ms)

// Add new variables for table-based storage
let columnResults = {}; // Store actual transformed column data by targetColumn
let activeColumnInfo = null; // Track the currently active column being worked with

// MEMORY MANAGEMENT: Add cleanup configuration
const MEMORY_CLEANUP_INTERVAL = 600000; // Clean up memory every 10 minutes
let memoryCleanupInterval = null;

// Default logic template for all logic fields - will be loaded from backend
let DEFAULT_LOGIC_TEMPLATE = `@prompt
-------------
@row
-------------
Analyze the provided data and return the most appropriate value based on the transformation logic described above.`;

/**
 * PERFORMANCE OPTIMIZATION: Setup memory cleanup for large datasets
 */
function setupMemoryCleanup() {
    if (memoryCleanupInterval) {
        clearInterval(memoryCleanupInterval);
    }
    
    memoryCleanupInterval = setInterval(() => {
        try {
            // Clear AG Grid cache if it exists
            if (window.unifiedPreviewGridApi) {
                // Force AG Grid to release memory
                window.unifiedPreviewGridApi.flushServerSideRowModel();
                window.unifiedPreviewGridApi.refreshCells({ force: true });
            }
            
            // Clear any large data structures that might be cached
            if (window.jobMappings && Object.keys(window.jobMappings).length > 100) {
                console.log('Clearing large jobMappings cache');
                window.jobMappings = {};
            }
            
            // Force garbage collection if available (Chrome with --js-flags="--expose-gc")
            if (window.gc && typeof window.gc === 'function') {
                window.gc();
                console.log('Memory cleanup: Forced garbage collection');
            }
            
            // Log memory usage if available
            if (performance.memory) {
                const memUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                console.log(`Memory cleanup: Current usage ${memUsage}MB`);
                
                // If memory usage is very high, warn user
                if (memUsage > 1000) { // More than 1GB
                    console.warn(`High memory usage detected: ${memUsage}MB`);
                    showAlert('warning', `High memory usage detected (${memUsage}MB). Consider reducing the dataset size or refreshing the page.`);
                }
            }
        } catch (e) {
            console.warn('Error during memory cleanup:', e);
        }
    }, MEMORY_CLEANUP_INTERVAL);
}

/**
 * Clear imported data, logic results, and resets parts of the UI, preserving mapping definitions.
 */
function clearImportedDataAndLogicResults() {
    if (!confirm("Are you sure you want to clear imported file data and the results of any applied logic? Your mapping definitions and column setup will be preserved.")) {
        return;
    }

    console.log("Clearing imported data and logic results...");

    // Clear data variables related to a specific import file/job
    originalData = [];
    // sourceColumns = []; // Keep sourceColumns if they are derived from mappingDefinitions or are static
    mappedData = [];
    columnResults = {}; // This holds the actual cell values after mapping/logic
    currentJobId = null;

    // Reset UI Elements related to file upload and job state
    $('#fileInput').val(null);
    if (typeof $('#fileUploadForm')[0] !== 'undefined') {
        $('#fileUploadForm')[0].reset();
    }
    $('#worksheetSelect').empty().append('<option value="">Choose a worksheet...</option>').val('');
    $('#worksheetSelectionSection').addClass('d-none');
    
    // Hide sections that display data, but keep mapping structure UI
    $('#mappedDataSection').addClass('d-none');

    // Clear AG Grid instances of row data
    if (window.unifiedPreviewGridApi) {
        window.unifiedPreviewGridApi.setRowData([]);
        // Columns should be re-established by updateUnifiedPreviewGrid based on existing mappings
    }
    if (window.gridApi && window.gridApi.gridOptions && window.gridApi.gridOptions.api) {
        window.gridApi.gridOptions.api.setRowData([]);
    }
    if (window.mappedGridApi && window.mappedGridApi.gridOptions && window.mappedGridApi.gridOptions.api) {
        window.mappedGridApi.gridOptions.api.setRowData([]);
    }

    // Call updateUnifiedPreviewGrid to reflect the cleared data state.
    // This function should use existing mappingDefinitions to show columns but no data.
    if (typeof updateUnifiedPreviewGrid === 'function') {
        updateUnifiedPreviewGrid();
    }
    // Ensure mapping headers are still correct (they shouldn't change if mappings are kept)
    // but call it just in case it also reacts to data presence.
    if (typeof updateUnifiedPreviewGridHeaderStructure === 'function') {
        updateUnifiedPreviewGridHeaderStructure();
    }
    if (typeof forceColumnAlignment === 'function') {
        forceColumnAlignment();
    }

    // Reset workflow step buttons to an initial state
    $('#workflowSteps').addClass('d-none').removeClass('d-flex'); // Hide or reset as appropriate
    $('#determineProductFamilyBtn').prop('disabled', true).removeClass('btn-success btn-warning').addClass('btn-info');
    $('#loadAdditionalColumnsBtn').prop('disabled', true).removeClass('btn-success btn-warning').addClass('btn-info');

    $('#saveProgressButton').hide();
    $('#currentJobNameDisplay').text('New Job (Mappings Kept)'); // Indicate mappings are kept

    // Clear the job_id from the URL
    try {
        history.pushState({ path: window.location.pathname }, '', window.location.pathname + (window.location.hash.split('?')[0] || ''));
    } catch (e) {
        console.warn("Could not clear job_id from URL using pushState immediately:", e);
        const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname + window.location.hash.split('?')[0];
        // Avoid full reload if possible: window.location.href = newUrl;
    }

    // Stop any running auto-save interval
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
        autoSaveInterval = null;
        console.log("Auto-save interval cleared.");
    }
    
    // Close any open modals that might show stale cell data
    $('.modal.fade[id*="CellEditModal"], .modal.fade[id*="rowSourceDataModal"]').modal('hide');


    showAlert('success', 'Imported data and logic results have been cleared. Your mappings are preserved.');
    console.log("Clearing imported data and logic results complete.");
}

/**
 * Fully Resets ALL import data, mappings, and UI to initial state.
 */
function fullResetImportWizard() {
    if (!confirm("Are you sure you want to clear ALL data, ALL mappings, and completely reset the import wizard? This action cannot be undone and will remove your current mapping setup.")) {
        return;
    }

    console.log("Performing full reset of import wizard...");

    // Clear global data variables
    originalData = [];
    sourceColumns = [];
    mappingDefinitions = [];
    mappedData = [];
    columnMappings = {};
    columnLogic = {};
    columnStringValues = {};
    columnLlmSettings = {};
    unifiedPreviewData = [];
    columnResults = {};
    activeColumnInfo = null;
    userNotes = ''; 
    currentJobId = null;

    if (window.jobMappings) {
        window.jobMappings = {};
    }
    if (window.cellLlmDetails) {
        window.cellLlmDetails = {};
    }
    if (window.deactivatedColumns) {
        window.deactivatedColumns = {};
    }

    // Reset UI Elements
    $('#fileInput').val(null);
    if (typeof $('#fileUploadForm')[0] !== 'undefined') {
        $('#fileUploadForm')[0].reset();
    }
    $('#worksheetSelect').empty().append('<option value="">Choose a worksheet...</option>').val('');
    $('#worksheetSelectionSection').addClass('d-none');
    
    $('#mappingSection').addClass('d-none');
    $('#horizontal-mapping-container').empty(); 
    $('#mappedDataSection').addClass('d-none');

    if (window.unifiedPreviewGridApi) {
        window.unifiedPreviewGridApi.setRowData([]);
        // Consider resetting columns if they are dynamically built and not from fixed definitions
        // window.unifiedPreviewGridApi.setColumnDefs([]); // Clears columns too
    }
    if (window.gridApi && window.gridApi.gridOptions && window.gridApi.gridOptions.api) {
        window.gridApi.gridOptions.api.setRowData([]);
        window.gridApi.gridOptions.api.setColumnDefs([]);
    }
    if (window.mappedGridApi && window.mappedGridApi.gridOptions && window.mappedGridApi.gridOptions.api) {
        window.mappedGridApi.gridOptions.api.setRowData([]);
        window.mappedGridApi.gridOptions.api.setColumnDefs([]);
    }

    if (typeof updateUnifiedPreviewGrid === 'function') {
        updateUnifiedPreviewGrid(); // This should show an empty grid with no columns if mappingDefinitions is empty
    }
    if (typeof updateUnifiedPreviewGridHeaderStructure === 'function') {
        updateUnifiedPreviewGridHeaderStructure(); 
    }
     if (typeof forceColumnAlignment === 'function') {
        forceColumnAlignment(); 
    }


    $('#workflowSteps').addClass('d-none').removeClass('d-flex');
    $('#determineProductFamilyBtn').prop('disabled', true).removeClass('btn-success btn-warning').addClass('btn-info');
    $('#loadAdditionalColumnsBtn').prop('disabled', true).removeClass('btn-success btn-warning').addClass('btn-info');
    
    $('#saveProgressButton').hide();
    $('#currentJobNameDisplay').text('New Job');
    
    try {
        history.pushState({ path: window.location.pathname }, '', window.location.pathname + (window.location.hash.split('?')[0] || ''));
    } catch (e) {
        console.warn("Could not clear job_id from URL using pushState immediately:", e);
        const newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname + window.location.hash.split('?')[0];
        // window.location.href = newUrl; 
    }

    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
        autoSaveInterval = null;
        console.log("Auto-save interval cleared.");
    }
    
    $('.modal').modal('hide');
    $('.alert-dismissible').alert('close');

    showAlert('success', 'All data and mappings have been cleared. The importer has been fully reset.');
    console.log("Full import wizard reset complete.");
}

/**
 * Load the default logic template from the backend
 */
async function loadDefaultLogicTemplate() {
    try {
        const response = await fetch('/api/import/get-default-logic-template');
        const data = await response.json();
        
        if (data.success && data.template) {
            DEFAULT_LOGIC_TEMPLATE = data.template;
            console.log('Loaded default logic template from backend');
        } else {
            console.warn('Could not load default logic template from backend:', data.message);
            // Keep the fallback template already defined above
        }
    } catch (error) {
        console.error('Error loading default logic template:', error);
        // Keep the fallback template already defined above
    }
}

/**
 * Sort mapping columns to put "Produktfamilie" first
 */
function sortMappingColumnsByProductFamily() {
    // Check if mapping columns exist
    const mappingContainer = $('#horizontal-mapping-container');
    if (!mappingContainer.length || mappingContainer.children().length === 0) {
        showAlert('warning', 'No columns available for sorting');
        return;
    }
    
    // Use the new moveColumnToFront function to properly handle order synchronization
    if (typeof moveColumnToFront === 'function') {
        moveColumnToFront('Produktfamilie');
        showAlert('success', '"Produktfamilie" column moved to first position');
    } else {
        // Fallback to the old method if moveColumnToFront is not available
        const produktfamilieColumn = $(`.mapping-column-cell[data-target-column="Produktfamilie"]`);
        
        if (produktfamilieColumn.length === 0) {
            showAlert('warning', '"Produktfamilie" column not found');
            return;
        }
        
        // Move the "Produktfamilie" column to the beginning of the container
        mappingContainer.prepend(produktfamilieColumn);
        
        // Apply visual feedback
        produktfamilieColumn.addClass('border-primary');
        setTimeout(() => {
            produktfamilieColumn.removeClass('border-primary');
        }, 2000);
        
        showAlert('success', '"Produktfamilie" column moved to first position');
    }
}

/**
 * Import Wizard Core Functions
 * PERFORMANCE OPTIMIZED: Added memory management and cleanup functions
 */

// PERFORMANCE: Memory management and cleanup functions
const MemoryManager = {
    /**
     * Clean up memory when switching jobs or during heavy operations
     */
    cleanupMemory() {
        console.log('🧹 Starting memory cleanup...');
        
        // Clear large data arrays
        if (window.originalData && window.originalData.length > 1000) {
            console.log(`📉 Clearing ${window.originalData.length} original data rows`);
            window.originalData = [];
        }
        
        if (window.mappedData && window.mappedData.length > 1000) {
            console.log(`📉 Clearing ${window.mappedData.length} mapped data rows`);
            window.mappedData = [];
        }
        
        // Clear column results for large datasets
        if (window.columnResults) {
            let clearedCount = 0;
            for (const [column, results] of Object.entries(window.columnResults)) {
                if (Array.isArray(results) && results.length > 100) {
                    delete window.columnResults[column];
                    clearedCount++;
                }
            }
            if (clearedCount > 0) {
                console.log(`📉 Cleared ${clearedCount} large column result arrays`);
            }
        }
        
        // Clear virtual column state
        if (window.virtualScrollState) {
            window.virtualScrollState.renderedColumns.clear();
        }
        
        // Destroy AG Grid to free memory
        if (typeof destroyUnifiedPreviewGrid === 'function') {
            destroyUnifiedPreviewGrid();
        }
        
        // Force garbage collection if available (Chrome with --js-flags="--expose-gc")
        if (window.gc && typeof window.gc === 'function') {
            setTimeout(() => {
                window.gc();
                console.log('🗑️ Forced garbage collection');
            }, 100);
        }
        
        console.log('✅ Memory cleanup completed');
    },
    
    /**
     * Monitor memory usage and trigger cleanup if needed
     */
    monitorMemory() {
        if (performance.memory) {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
            const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
            
            console.log(`💾 Memory usage: ${usedMB}MB / ${totalMB}MB`);
            
            // Update UI indicator
            this.updateMemoryIndicator(usedMB, totalMB);
            
            // If using more than 200MB, trigger cleanup
            if (usedMB > 200) {
                console.log('⚠️ High memory usage detected, triggering cleanup');
                this.cleanupMemory();
            }
        }
    },
    
    /**
     * Update the memory usage indicator in the UI
     */
    updateMemoryIndicator(usedMB, totalMB) {
        const indicator = $('#memoryIndicator');
        const usageSpan = $('#memoryUsage');
        
        if (indicator.length && usageSpan.length) {
            usageSpan.text(`${usedMB}MB`);
            
            // Show indicator if memory usage is significant
            if (usedMB > 50) {
                indicator.show();
                
                // Color code based on usage
                indicator.removeClass('btn-outline-secondary btn-outline-warning btn-outline-danger');
                if (usedMB > 300) {
                    indicator.addClass('btn-outline-danger');
                    indicator.attr('title', `High memory usage: ${usedMB}MB / ${totalMB}MB. Consider refreshing the page.`);
                } else if (usedMB > 150) {
                    indicator.addClass('btn-outline-warning');
                    indicator.attr('title', `Moderate memory usage: ${usedMB}MB / ${totalMB}MB`);
                } else {
                    indicator.addClass('btn-outline-secondary');
                    indicator.attr('title', `Memory usage: ${usedMB}MB / ${totalMB}MB`);
                }
            } else {
                indicator.hide();
            }
        }
    },
    
    /**
     * Setup periodic memory monitoring
     */
    setupMonitoring() {
        // Monitor memory every 30 seconds
        setInterval(() => {
            this.monitorMemory();
        }, 30000);
        
        console.log('📊 Memory monitoring started');
    },
    
    /**
     * Get current memory statistics
     */
    getMemoryStats() {
        const stats = {
            originalDataSize: window.originalData ? window.originalData.length : 0,
            mappedDataSize: window.mappedData ? window.mappedData.length : 0,
            columnResultsCount: window.columnResults ? Object.keys(window.columnResults).length : 0,
            virtualColumnsRendered: window.virtualScrollState ? window.virtualScrollState.renderedColumns.size : 0
        };
        
        if (performance.memory) {
            const memory = performance.memory;
            stats.jsHeapUsed = Math.round(memory.usedJSHeapSize / 1048576);
            stats.jsHeapTotal = Math.round(memory.totalJSHeapSize / 1048576);
            stats.jsHeapLimit = Math.round(memory.jsHeapSizeLimit / 1048576);
        }
        
        return stats;
    }
};

// Start memory monitoring when the page loads
$(document).ready(() => {
    MemoryManager.setupMonitoring();
    
    // Add memory stats to console for debugging
    window.getMemoryStats = () => {
        const stats = MemoryManager.getMemoryStats();
        console.table(stats);
        return stats;
    };
    
    // Add manual cleanup function to global scope
    window.cleanupMemory = () => MemoryManager.cleanupMemory();
});

// PERFORMANCE: Debounced functions for heavy operations
const DebouncedOperations = {
    /**
     * Debounced grid update to prevent excessive re-renders
     */
    updateGrid: debounce(() => {
        if (typeof updateUnifiedPreviewGrid === 'function') {
            updateUnifiedPreviewGrid();
        }
    }, 100),
    
    /**
     * Debounced column status update
     */
    updateColumnStatus: debounce((columnName, statusElement) => {
        if (typeof updateColumnDisplayStatus === 'function') {
            updateColumnDisplayStatus(columnName, statusElement);
        }
    }, 50),
    
    /**
     * Debounced save operation
     */
    saveJob: debounce(() => {
        if (typeof saveCurrentJobProgress === 'function') {
            saveCurrentJobProgress(true); // silent save
        }
    }, 1000)
};

/**
 * Debounce utility function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const context = this;
        const later = () => {
            timeout = null;
            func.apply(context, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Make debounced operations available globally
window.DebouncedOperations = DebouncedOperations;

// Document ready event
$(document).ready(function() {
    // Load default logic template from backend
    loadDefaultLogicTemplate();
    
    // Load enabled models from OpenRouter
    loadEnabledModels();
    
    // Check for job_id in URL
    const urlParams = new URLSearchParams(window.location.search);
    const jobIdFromUrl = urlParams.get('job_id');
    if (jobIdFromUrl) {
        currentJobId = jobIdFromUrl;
        loadImportJob(currentJobId);
        $('#saveProgressButton').show(); // Show save button if loading a job
        
        // Set job ID in product family handler
        if (window.productFamilyHandler) {
            window.productFamilyHandler.setCurrentJobId(currentJobId);
        }
    }
    
    // Initialize collapsible sections
    initializeCollapsible();
    
    // Initialize notes functionality
    initializeNotes();
    
    // Form submit handler
    $('#fileUploadForm').on('submit', function(e) {
        e.preventDefault();
        handleFileUpload();
    });
    
    // Apply mapping button click
    $('#applyMappingButton').on('click', function() {
        applyColumnMapping();
    });
    
    // Export button click
    $('#exportButton').on('click', function() {
        exportMappedData();
    });
    
    // AI column mapping button click
    $('#aiMappingButton').on('click', function() {
        if (typeof openEnhancedAiMapping === 'function') {
            openEnhancedAiMapping();
        } else {
            console.error('openEnhancedAiMapping function not available - checking for errors in enhanced_ai_mapping_handler.js');
            showAlert('error', 'AI Mapping feature not available. Please check browser console for JavaScript errors.');
        }
    });
    
    // Toggle optional columns button click
    $('#toggleOptionalColumnsButton').on('click', function() {
        toggleOptionalColumns();
    });
    
    // Execute AI mapping button click
    $('#executeAiMappingBtn').on('click', function() {
        executeAiColumnMapping();
    });
    
    // Include sample rows toggle
    $('#includeSampleRowsToggle').on('change', function() {
        updateAiMappingPrompt();
    });
    
    // Save Progress button click
    $('#saveProgressButton').on('click', function() {
        saveCurrentJobProgress();
    });
    
    $('#editJobNameIcon').on('click', function() {
        if (!currentJobId) return;
        const currentName = $('#currentJobNameDisplay').text();
        const newName = prompt("Enter new name for this job:", currentName);
        if (newName && newName.trim() !== '' && newName.trim() !== currentName) {
            renameCurrentJob(newName.trim());
        }
    });
    

    
    // New button handler for Determine Product Family - DEPRECATED
    // This functionality is now handled by the ProductFamilyHandler
    $('#determineProductFamilyBtn').on('click', function() {
        // Show a message that this is now handled by the new workflow
        showAlert('info', 'Product family determination is now handled by the new workflow buttons above. Please use "Determine Product Family" in the workflow section.');
        
        // Fallback: just sort the columns for visual organization
        sortMappingColumnsByProductFamily();
    });
    
    // Handler for Load Additional Columns button - DEPRECATED
    // This functionality is now handled by the ProductFamilyHandler
    $('#loadAdditionalColumnsBtn').on('click', function() {
        // The actual functionality is now in ProductFamilyHandler
        // This is just a fallback for UI compatibility
        const $btn = $(this);
        const originalText = $btn.html();
        $btn.prop('disabled', true)
            .html('<span class="spinner-border spinner-border-sm"></span> Loading...');
        
        try {
            // Process columns based on Produktfamilie value
            processAdditionalColumns();
            
            // Enable the next step
            $('#mapDataBtn').prop('disabled', false);
            
            // Success message
            showAlert('success', 'Additional columns loaded successfully', 10000);
            
            // Update button state
            $btn.html(originalText).addClass('btn-success').removeClass('btn-info');
        } catch (error) {
            console.error('Error loading additional columns:', error);
            showAlert('error', `Error loading additional columns: ${error.message}`);
            
            // Reset button state
            $btn.prop('disabled', false).html(originalText);
        }
    });
    
    // Reference tag click handler
    $(document).on('click', '.reference-tag', function() {
        const reference = $(this).text();
        const input = $(this).closest('.logic-container').find('.logic-input');
        
        // Insert reference at cursor position
        if (input && input.length > 0) {
            insertAtCursor(input[0], reference);
        }
    });
    
    // Updated event listener for the #clearAllDataBtn button
    $('#clearAllDataBtn').on('click', function() {
        clearImportedDataAndLogicResults(); // Calls the new function
    });
    
    // Initialize the AI mapping modal
    if (document.getElementById('aiMappingModal')) {
      aiMappingModal = new bootstrap.Modal(document.getElementById('aiMappingModal'));
    }
        
    // Setup auto-save functionality when a job is loaded
    if (currentJobId) {
        setupAutoSave();
    }
    
    // Add window focus event to refresh models when returning from Model Settings
    $(window).on('focus', function() {
        if (typeof refreshEnabledModels === 'function') {
            refreshEnabledModels();
        }
    });
}); 